# Mean Reversion Trading System

A comprehensive cryptocurrency and stock factor research and backtesting platform focused on mean reversion trading strategies.

## Overview

This project implements a sophisticated mean reversion trading system that:
- Identifies overbought/oversold conditions when asset prices deviate from historical means
- Executes contrarian trades (short when overbought, long when oversold)
- Provides comprehensive backtesting and performance analysis
- Supports both cryptocurrency and traditional stock market data

## Project Structure

```
mean_reversion/
├── src/
│   ├── data/                    # Data infrastructure
│   │   ├── providers/           # Data source integrations
│   │   ├── storage/             # Data storage and management
│   │   └── preprocessing/       # Data cleaning and preparation
│   ├── factors/                 # Factor research framework
│   │   ├── indicators/          # Technical indicators
│   │   ├── signals/             # Signal generation
│   │   └── analysis/            # Statistical analysis tools
│   ├── backtesting/             # Backtesting engine
│   │   ├── engine/              # Core backtesting logic
│   │   ├── metrics/             # Performance metrics
│   │   └── risk/                # Risk management
│   ├── strategies/              # Trading strategies
│   │   ├── mean_reversion/      # Mean reversion strategies
│   │   └── base/                # Base strategy classes
│   ├── portfolio/               # Portfolio management
│   └── utils/                   # Utility functions
├── tests/                       # Unit tests
├── configs/                     # Configuration files
├── data/                        # Data storage
├── notebooks/                   # Jupyter notebooks for research
├── docs/                        # Documentation
└── examples/                    # Usage examples
```

## Core Features

### Data Infrastructure
- Multi-source data ingestion (Yahoo Finance, Alpha Vantage, Binance, etc.)
- Historical and real-time data support
- Efficient data storage and retrieval
- Data quality validation and cleaning

### Factor Research Framework
- Multiple mean calculation methods (SMA, EMA, LWMA)
- Volatility and deviation measurements
- Statistical significance testing
- Signal strength and confidence scoring

### Backtesting Engine
- Historical strategy simulation
- Comprehensive performance metrics
- Risk management and position sizing
- Transaction cost modeling
- Portfolio-level analysis

### Mean Reversion Strategies
- Z-score based mean reversion
- Bollinger Bands mean reversion
- RSI-based contrarian strategies
- Multi-timeframe analysis

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd mean-reversion

# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

## Quick Start

```python
from src.strategies.mean_reversion import ZScoreMeanReversion
from src.backtesting.engine import BacktestEngine
from src.data.providers import YahooFinanceProvider

# Initialize data provider
data_provider = YahooFinanceProvider()

# Create strategy
strategy = ZScoreMeanReversion(
    lookback_period=20,
    entry_threshold=2.0,
    exit_threshold=0.5
)

# Run backtest
engine = BacktestEngine(
    strategy=strategy,
    data_provider=data_provider,
    initial_capital=100000
)

results = engine.run_backtest(
    symbols=['AAPL', 'MSFT', 'BTC-USD'],
    start_date='2020-01-01',
    end_date='2023-12-31'
)

# Analyze results
print(f"Total Return: {results.total_return:.2%}")
print(f"Sharpe Ratio: {results.sharpe_ratio:.2f}")
print(f"Max Drawdown: {results.max_drawdown:.2%}")
```

## Configuration

The system uses YAML configuration files for easy parameter management:

```yaml
# configs/strategy_config.yaml
mean_reversion:
  lookback_period: 20
  entry_threshold: 2.0
  exit_threshold: 0.5
  position_size: 0.1
  max_positions: 10

risk_management:
  max_position_size: 0.2
  stop_loss: 0.05
  max_drawdown: 0.15
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
