"""Base data storage interface for the Mean Reversion Trading System."""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Dict, Any
import pandas as pd

from ..models import PriceData, Symbol, MarketData


class DataStorage(ABC):
    """Abstract base class for data storage implementations."""
    
    @abstractmethod
    def connect(self) -> None:
        """Establish connection to storage backend."""
        pass
    
    @abstractmethod
    def disconnect(self) -> None:
        """Close connection to storage backend."""
        pass
    
    @abstractmethod
    def create_tables(self) -> None:
        """Create necessary tables/collections in storage."""
        pass
    
    # Symbol management
    @abstractmethod
    def save_symbol(self, symbol: Symbol) -> None:
        """Save symbol metadata."""
        pass
    
    @abstractmethod
    def get_symbol(self, symbol: str) -> Optional[Symbol]:
        """Get symbol metadata."""
        pass
    
    @abstractmethod
    def list_symbols(self, asset_type: Optional[str] = None) -> List[Symbol]:
        """List all symbols, optionally filtered by asset type."""
        pass
    
    @abstractmethod
    def delete_symbol(self, symbol: str) -> None:
        """Delete symbol and all associated data."""
        pass
    
    # Price data management
    @abstractmethod
    def save_price_data(self, price_data: List[PriceData]) -> None:
        """Save price data."""
        pass
    
    @abstractmethod
    def get_price_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> Optional[MarketData]:
        """Get price data for symbol within date range."""
        pass
    
    @abstractmethod
    def get_latest_price(self, symbol: str) -> Optional[PriceData]:
        """Get latest price data for symbol."""
        pass
    
    @abstractmethod
    def get_price_range(self, symbol: str) -> tuple[Optional[datetime], Optional[datetime]]:
        """Get date range of available price data for symbol."""
        pass
    
    @abstractmethod
    def delete_price_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> None:
        """Delete price data for symbol within date range."""
        pass
    
    # Bulk operations
    @abstractmethod
    def bulk_save_price_data(self, data: Dict[str, List[PriceData]]) -> None:
        """Save price data for multiple symbols efficiently."""
        pass
    
    @abstractmethod
    def get_multiple_price_data(
        self,
        symbols: List[str],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, MarketData]:
        """Get price data for multiple symbols."""
        pass
    
    # Utility methods
    @abstractmethod
    def get_storage_info(self) -> Dict[str, Any]:
        """Get information about storage usage and statistics."""
        pass
    
    @abstractmethod
    def optimize_storage(self) -> None:
        """Optimize storage (vacuum, reindex, etc.)."""
        pass
    
    @abstractmethod
    def backup_data(self, backup_path: str) -> None:
        """Create backup of data."""
        pass
    
    @abstractmethod
    def restore_data(self, backup_path: str) -> None:
        """Restore data from backup."""
        pass
    
    # Context manager support
    def __enter__(self):
        """Enter context manager."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        self.disconnect()
    
    # Helper methods that can be overridden
    def save_market_data(self, market_data: MarketData) -> None:
        """Save MarketData object."""
        price_data_list = market_data.to_price_data_list()
        self.save_price_data(price_data_list)
    
    def update_symbol_metadata(self, symbol: str, metadata: Dict[str, Any]) -> None:
        """Update symbol metadata."""
        existing_symbol = self.get_symbol(symbol)
        if existing_symbol:
            existing_symbol.metadata.update(metadata)
            self.save_symbol(existing_symbol)
        else:
            new_symbol = Symbol(symbol=symbol, metadata=metadata)
            self.save_symbol(new_symbol)
    
    def get_symbols_by_type(self, asset_type: str) -> List[Symbol]:
        """Get symbols filtered by asset type."""
        return [s for s in self.list_symbols() if s.asset_type == asset_type]
    
    def has_data(self, symbol: str, start_date: datetime, end_date: datetime) -> bool:
        """Check if data exists for symbol in date range."""
        data_start, data_end = self.get_price_range(symbol)
        if data_start is None or data_end is None:
            return False
        return data_start <= start_date and data_end >= end_date
    
    def get_missing_dates(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        frequency: str = 'D'
    ) -> List[datetime]:
        """Get list of missing dates for symbol in date range."""
        market_data = self.get_price_data(symbol, start_date, end_date)
        if market_data is None:
            # All dates are missing
            return pd.date_range(start_date, end_date, freq=frequency).tolist()
        
        # Find gaps in data
        expected_dates = pd.date_range(start_date, end_date, freq=frequency)
        actual_dates = market_data.data.index
        missing_dates = expected_dates.difference(actual_dates)
        
        return missing_dates.tolist()
