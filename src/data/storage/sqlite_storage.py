"""SQLite storage implementation for the Mean Reversion Trading System."""

import sqlite3
import json
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
import pandas as pd

from .base import DataStorage
from ..models import PriceData, Symbol, MarketData
from ...utils.logger import get_logger
from ...utils.decorators import retry

logger = get_logger(__name__)


class SQLiteStorage(DataStorage):
    """SQLite implementation of data storage."""
    
    def __init__(self, db_path: str = "data/market_data.db"):
        """
        Initialize SQLite storage.
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self.connection: Optional[sqlite3.Connection] = None
        
        # Ensure directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def connect(self) -> None:
        """Establish connection to SQLite database."""
        try:
            self.connection = sqlite3.connect(
                str(self.db_path),
                check_same_thread=False,
                timeout=30.0
            )
            self.connection.row_factory = sqlite3.Row
            
            # Enable WAL mode for better concurrency
            self.connection.execute("PRAGMA journal_mode=WAL")
            self.connection.execute("PRAGMA synchronous=NORMAL")
            self.connection.execute("PRAGMA cache_size=10000")
            self.connection.execute("PRAGMA temp_store=MEMORY")
            
            logger.info(f"Connected to SQLite database: {self.db_path}")
            
        except sqlite3.Error as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def disconnect(self) -> None:
        """Close connection to SQLite database."""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Disconnected from SQLite database")
    
    def create_tables(self) -> None:
        """Create necessary tables in SQLite database."""
        if not self.connection:
            raise RuntimeError("Not connected to database")
        
        cursor = self.connection.cursor()
        
        # Create symbols table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS symbols (
                symbol TEXT PRIMARY KEY,
                name TEXT,
                exchange TEXT,
                asset_type TEXT NOT NULL DEFAULT 'stock',
                currency TEXT NOT NULL DEFAULT 'USD',
                sector TEXT,
                industry TEXT,
                market_cap REAL,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create price_data table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS price_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp TIMESTAMP NOT NULL,
                open REAL NOT NULL,
                high REAL NOT NULL,
                low REAL NOT NULL,
                close REAL NOT NULL,
                volume REAL NOT NULL,
                adjusted_close REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (symbol) REFERENCES symbols (symbol),
                UNIQUE(symbol, timestamp)
            )
        """)
        
        # Create indexes for better performance
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_price_data_symbol_timestamp 
            ON price_data (symbol, timestamp)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_price_data_timestamp 
            ON price_data (timestamp)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_symbols_asset_type 
            ON symbols (asset_type)
        """)
        
        # Create trigger to update updated_at timestamp
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_symbols_timestamp 
            AFTER UPDATE ON symbols
            BEGIN
                UPDATE symbols SET updated_at = CURRENT_TIMESTAMP WHERE symbol = NEW.symbol;
            END
        """)
        
        self.connection.commit()
        logger.info("Database tables created successfully")
    
    @retry(max_attempts=3, delay=0.1)
    def save_symbol(self, symbol: Symbol) -> None:
        """Save symbol metadata."""
        if not self.connection:
            raise RuntimeError("Not connected to database")
        
        cursor = self.connection.cursor()
        
        metadata_json = json.dumps(symbol.metadata) if symbol.metadata else None
        
        cursor.execute("""
            INSERT OR REPLACE INTO symbols 
            (symbol, name, exchange, asset_type, currency, sector, industry, 
             market_cap, is_active, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            symbol.symbol, symbol.name, symbol.exchange, symbol.asset_type,
            symbol.currency, symbol.sector, symbol.industry, symbol.market_cap,
            symbol.is_active, metadata_json
        ))
        
        self.connection.commit()
        logger.debug(f"Saved symbol: {symbol.symbol}")
    
    def get_symbol(self, symbol: str) -> Optional[Symbol]:
        """Get symbol metadata."""
        if not self.connection:
            raise RuntimeError("Not connected to database")
        
        cursor = self.connection.cursor()
        cursor.execute("SELECT * FROM symbols WHERE symbol = ?", (symbol,))
        row = cursor.fetchone()
        
        if row:
            metadata = json.loads(row['metadata']) if row['metadata'] else {}
            return Symbol(
                symbol=row['symbol'],
                name=row['name'],
                exchange=row['exchange'],
                asset_type=row['asset_type'],
                currency=row['currency'],
                sector=row['sector'],
                industry=row['industry'],
                market_cap=row['market_cap'],
                is_active=bool(row['is_active']),
                metadata=metadata
            )
        
        return None
    
    def list_symbols(self, asset_type: Optional[str] = None) -> List[Symbol]:
        """List all symbols, optionally filtered by asset type."""
        if not self.connection:
            raise RuntimeError("Not connected to database")
        
        cursor = self.connection.cursor()
        
        if asset_type:
            cursor.execute("SELECT * FROM symbols WHERE asset_type = ? ORDER BY symbol", (asset_type,))
        else:
            cursor.execute("SELECT * FROM symbols ORDER BY symbol")
        
        symbols = []
        for row in cursor.fetchall():
            metadata = json.loads(row['metadata']) if row['metadata'] else {}
            symbol = Symbol(
                symbol=row['symbol'],
                name=row['name'],
                exchange=row['exchange'],
                asset_type=row['asset_type'],
                currency=row['currency'],
                sector=row['sector'],
                industry=row['industry'],
                market_cap=row['market_cap'],
                is_active=bool(row['is_active']),
                metadata=metadata
            )
            symbols.append(symbol)
        
        return symbols
    
    def delete_symbol(self, symbol: str) -> None:
        """Delete symbol and all associated data."""
        if not self.connection:
            raise RuntimeError("Not connected to database")
        
        cursor = self.connection.cursor()
        
        # Delete price data first (foreign key constraint)
        cursor.execute("DELETE FROM price_data WHERE symbol = ?", (symbol,))
        
        # Delete symbol
        cursor.execute("DELETE FROM symbols WHERE symbol = ?", (symbol,))
        
        self.connection.commit()
        logger.info(f"Deleted symbol and all data: {symbol}")
    
    @retry(max_attempts=3, delay=0.1)
    def save_price_data(self, price_data: List[PriceData]) -> None:
        """Save price data."""
        if not self.connection:
            raise RuntimeError("Not connected to database")
        
        if not price_data:
            return
        
        cursor = self.connection.cursor()
        
        # Prepare data for bulk insert
        data_tuples = []
        for pd_item in price_data:
            data_tuples.append((
                pd_item.symbol,
                pd_item.timestamp,
                pd_item.open,
                pd_item.high,
                pd_item.low,
                pd_item.close,
                pd_item.volume,
                pd_item.adjusted_close
            ))
        
        # Use INSERT OR REPLACE to handle duplicates
        cursor.executemany("""
            INSERT OR REPLACE INTO price_data 
            (symbol, timestamp, open, high, low, close, volume, adjusted_close)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, data_tuples)
        
        self.connection.commit()
        logger.debug(f"Saved {len(price_data)} price data records")
    
    def get_price_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> Optional[MarketData]:
        """Get price data for symbol within date range."""
        if not self.connection:
            raise RuntimeError("Not connected to database")
        
        # Build query
        query = """
            SELECT timestamp, open, high, low, close, volume, adjusted_close
            FROM price_data 
            WHERE symbol = ?
        """
        params = [symbol]
        
        if start_date:
            query += " AND timestamp >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND timestamp <= ?"
            params.append(end_date)
        
        query += " ORDER BY timestamp"
        
        if limit:
            query += " LIMIT ?"
            params.append(limit)
        
        # Execute query
        df = pd.read_sql_query(query, self.connection, params=params, parse_dates=['timestamp'])
        
        if df.empty:
            return None
        
        # Set timestamp as index
        df.set_index('timestamp', inplace=True)
        
        return MarketData(df, symbol)

    def get_latest_price(self, symbol: str) -> Optional[PriceData]:
        """Get latest price data for symbol."""
        if not self.connection:
            raise RuntimeError("Not connected to database")

        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT * FROM price_data
            WHERE symbol = ?
            ORDER BY timestamp DESC
            LIMIT 1
        """, (symbol,))

        row = cursor.fetchone()
        if row:
            return PriceData(
                symbol=row['symbol'],
                timestamp=datetime.fromisoformat(row['timestamp']),
                open=row['open'],
                high=row['high'],
                low=row['low'],
                close=row['close'],
                volume=row['volume'],
                adjusted_close=row['adjusted_close']
            )

        return None

    def get_price_range(self, symbol: str) -> tuple[Optional[datetime], Optional[datetime]]:
        """Get date range of available price data for symbol."""
        if not self.connection:
            raise RuntimeError("Not connected to database")

        cursor = self.connection.cursor()
        cursor.execute("""
            SELECT MIN(timestamp) as start_date, MAX(timestamp) as end_date
            FROM price_data
            WHERE symbol = ?
        """, (symbol,))

        row = cursor.fetchone()
        if row and row['start_date']:
            start_date = datetime.fromisoformat(row['start_date'])
            end_date = datetime.fromisoformat(row['end_date'])
            return start_date, end_date

        return None, None

    def delete_price_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> None:
        """Delete price data for symbol within date range."""
        if not self.connection:
            raise RuntimeError("Not connected to database")

        cursor = self.connection.cursor()

        query = "DELETE FROM price_data WHERE symbol = ?"
        params = [symbol]

        if start_date:
            query += " AND timestamp >= ?"
            params.append(start_date)

        if end_date:
            query += " AND timestamp <= ?"
            params.append(end_date)

        cursor.execute(query, params)
        self.connection.commit()

        deleted_count = cursor.rowcount
        logger.info(f"Deleted {deleted_count} price data records for {symbol}")

    def bulk_save_price_data(self, data: Dict[str, List[PriceData]]) -> None:
        """Save price data for multiple symbols efficiently."""
        if not self.connection:
            raise RuntimeError("Not connected to database")

        all_price_data = []
        for symbol, price_data_list in data.items():
            all_price_data.extend(price_data_list)

        if all_price_data:
            self.save_price_data(all_price_data)

    def get_multiple_price_data(
        self,
        symbols: List[str],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, MarketData]:
        """Get price data for multiple symbols."""
        result = {}

        for symbol in symbols:
            market_data = self.get_price_data(symbol, start_date, end_date)
            if market_data:
                result[symbol] = market_data

        return result

    def get_storage_info(self) -> Dict[str, Any]:
        """Get information about storage usage and statistics."""
        if not self.connection:
            raise RuntimeError("Not connected to database")

        cursor = self.connection.cursor()

        # Get database size
        db_size = self.db_path.stat().st_size if self.db_path.exists() else 0

        # Get table counts
        cursor.execute("SELECT COUNT(*) as count FROM symbols")
        symbol_count = cursor.fetchone()['count']

        cursor.execute("SELECT COUNT(*) as count FROM price_data")
        price_data_count = cursor.fetchone()['count']

        # Get date range
        cursor.execute("SELECT MIN(timestamp) as min_date, MAX(timestamp) as max_date FROM price_data")
        date_range = cursor.fetchone()

        return {
            'database_size_bytes': db_size,
            'database_size_mb': round(db_size / (1024 * 1024), 2),
            'symbol_count': symbol_count,
            'price_data_count': price_data_count,
            'date_range': {
                'start': date_range['min_date'],
                'end': date_range['max_date']
            },
            'database_path': str(self.db_path)
        }

    def optimize_storage(self) -> None:
        """Optimize storage (vacuum, reindex, etc.)."""
        if not self.connection:
            raise RuntimeError("Not connected to database")

        logger.info("Optimizing database...")

        # Vacuum database
        self.connection.execute("VACUUM")

        # Analyze tables for query optimization
        self.connection.execute("ANALYZE")

        # Reindex
        self.connection.execute("REINDEX")

        self.connection.commit()
        logger.info("Database optimization completed")

    def backup_data(self, backup_path: str) -> None:
        """Create backup of data."""
        if not self.connection:
            raise RuntimeError("Not connected to database")

        backup_path = Path(backup_path)
        backup_path.parent.mkdir(parents=True, exist_ok=True)

        # Create backup connection
        backup_conn = sqlite3.connect(str(backup_path))

        try:
            # Copy database
            self.connection.backup(backup_conn)
            logger.info(f"Database backed up to: {backup_path}")
        finally:
            backup_conn.close()

    def restore_data(self, backup_path: str) -> None:
        """Restore data from backup."""
        backup_path = Path(backup_path)

        if not backup_path.exists():
            raise FileNotFoundError(f"Backup file not found: {backup_path}")

        # Close current connection
        if self.connection:
            self.disconnect()

        # Replace current database with backup
        import shutil
        shutil.copy2(backup_path, self.db_path)

        # Reconnect
        self.connect()
        logger.info(f"Database restored from: {backup_path}")
