"""Data models for the Mean Reversion Trading System."""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any
import pandas as pd
import numpy as np


@dataclass
class Symbol:
    """Represents a trading symbol with metadata."""
    
    symbol: str
    name: Optional[str] = None
    exchange: Optional[str] = None
    asset_type: str = "stock"  # stock, crypto, forex, commodity
    currency: str = "USD"
    sector: Optional[str] = None
    industry: Optional[str] = None
    market_cap: Optional[float] = None
    is_active: bool = True
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Validate and normalize symbol data."""
        self.symbol = self.symbol.upper().strip()
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'symbol': self.symbol,
            'name': self.name,
            'exchange': self.exchange,
            'asset_type': self.asset_type,
            'currency': self.currency,
            'sector': self.sector,
            'industry': self.industry,
            'market_cap': self.market_cap,
            'is_active': self.is_active,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Symbol':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class PriceData:
    """Represents OHLCV price data for a single period."""
    
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    adjusted_close: Optional[float] = None
    
    def __post_init__(self):
        """Validate price data."""
        if self.adjusted_close is None:
            self.adjusted_close = self.close
        
        # Basic validation
        if self.high < self.low:
            raise ValueError(f"High ({self.high}) cannot be less than low ({self.low})")
        
        if not (self.low <= self.open <= self.high):
            raise ValueError(f"Open ({self.open}) must be between low ({self.low}) and high ({self.high})")
        
        if not (self.low <= self.close <= self.high):
            raise ValueError(f"Close ({self.close}) must be between low ({self.low}) and high ({self.high})")
        
        if self.volume < 0:
            raise ValueError(f"Volume ({self.volume}) cannot be negative")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp,
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume,
            'adjusted_close': self.adjusted_close
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PriceData':
        """Create from dictionary."""
        return cls(**data)
    
    def returns(self, previous_close: float) -> float:
        """Calculate simple return from previous close."""
        if previous_close <= 0:
            return 0.0
        return (self.close - previous_close) / previous_close
    
    def log_returns(self, previous_close: float) -> float:
        """Calculate log return from previous close."""
        if previous_close <= 0 or self.close <= 0:
            return 0.0
        return np.log(self.close / previous_close)


class MarketData:
    """Container for market data with utility methods."""
    
    def __init__(self, data: pd.DataFrame, symbol: str):
        """
        Initialize market data container.
        
        Args:
            data: DataFrame with OHLCV data
            symbol: Trading symbol
        """
        self.symbol = symbol
        self.data = data.copy()
        self._validate_data()
        self._prepare_data()
    
    def _validate_data(self):
        """Validate the input data."""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = set(required_columns) - set(self.data.columns)
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        if self.data.empty:
            raise ValueError("Data cannot be empty")
        
        # Ensure datetime index
        if not isinstance(self.data.index, pd.DatetimeIndex):
            if 'timestamp' in self.data.columns:
                self.data.set_index('timestamp', inplace=True)
            else:
                raise ValueError("Data must have datetime index or timestamp column")
    
    def _prepare_data(self):
        """Prepare data with additional columns."""
        # Add adjusted close if not present
        if 'adjusted_close' not in self.data.columns:
            self.data['adjusted_close'] = self.data['close']
        
        # Sort by timestamp
        self.data.sort_index(inplace=True)
        
        # Add basic derived columns
        self.data['returns'] = self.data['adjusted_close'].pct_change()
        self.data['log_returns'] = np.log(self.data['adjusted_close'] / self.data['adjusted_close'].shift(1))
        self.data['typical_price'] = (self.data['high'] + self.data['low'] + self.data['close']) / 3
        self.data['range'] = self.data['high'] - self.data['low']
        self.data['range_pct'] = self.data['range'] / self.data['close']
    
    def get_price_data(self, start_date: Optional[datetime] = None, 
                      end_date: Optional[datetime] = None) -> pd.DataFrame:
        """
        Get price data for specified date range.
        
        Args:
            start_date: Start date (inclusive)
            end_date: End date (inclusive)
            
        Returns:
            Filtered DataFrame
        """
        data = self.data.copy()
        
        if start_date:
            data = data[data.index >= start_date]
        
        if end_date:
            data = data[data.index <= end_date]
        
        return data
    
    def get_returns(self, start_date: Optional[datetime] = None,
                   end_date: Optional[datetime] = None,
                   log_returns: bool = False) -> pd.Series:
        """
        Get returns series.
        
        Args:
            start_date: Start date
            end_date: End date
            log_returns: Whether to use log returns
            
        Returns:
            Returns series
        """
        data = self.get_price_data(start_date, end_date)
        return data['log_returns'] if log_returns else data['returns']
    
    def resample(self, frequency: str) -> 'MarketData':
        """
        Resample data to different frequency.
        
        Args:
            frequency: Pandas frequency string (e.g., '1D', '1H', '5T')
            
        Returns:
            New MarketData instance with resampled data
        """
        resampled = self.data.resample(frequency).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'adjusted_close': 'last'
        }).dropna()
        
        return MarketData(resampled, self.symbol)
    
    def to_price_data_list(self) -> list[PriceData]:
        """Convert to list of PriceData objects."""
        price_data_list = []
        
        for timestamp, row in self.data.iterrows():
            price_data = PriceData(
                symbol=self.symbol,
                timestamp=timestamp,
                open=row['open'],
                high=row['high'],
                low=row['low'],
                close=row['close'],
                volume=row['volume'],
                adjusted_close=row.get('adjusted_close', row['close'])
            )
            price_data_list.append(price_data)
        
        return price_data_list
    
    @property
    def start_date(self) -> datetime:
        """Get start date of data."""
        return self.data.index.min()
    
    @property
    def end_date(self) -> datetime:
        """Get end date of data."""
        return self.data.index.max()
    
    @property
    def length(self) -> int:
        """Get number of data points."""
        return len(self.data)
    
    def __len__(self) -> int:
        """Return length of data."""
        return len(self.data)
    
    def __repr__(self) -> str:
        """String representation."""
        return f"MarketData(symbol={self.symbol}, length={len(self.data)}, start={self.start_date.date()}, end={self.end_date.date()})"
