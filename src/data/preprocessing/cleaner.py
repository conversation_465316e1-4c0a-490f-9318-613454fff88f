"""Data cleaning utilities for the Mean Reversion Trading System."""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, List
from datetime import datetime

from ..models import MarketData
from ...utils.logger import get_logger

logger = get_logger(__name__)


class DataCleaner:
    """Data cleaning and preprocessing utilities."""
    
    def __init__(self):
        """Initialize data cleaner."""
        pass
    
    def clean_price_data(
        self,
        market_data: MarketData,
        remove_outliers: bool = True,
        fill_missing: bool = True,
        validate_ohlc: bool = True
    ) -> MarketData:
        """
        Clean price data by removing outliers, filling missing values, etc.
        
        Args:
            market_data: MarketData to clean
            remove_outliers: Whether to remove price outliers
            fill_missing: Whether to fill missing values
            validate_ohlc: Whether to validate OHLC relationships
            
        Returns:
            Cleaned MarketData
        """
        df = market_data.data.copy()
        symbol = market_data.symbol
        
        logger.info(f"Cleaning price data for {symbol}, initial shape: {df.shape}")
        
        # Remove rows with all NaN values
        df.dropna(how='all', inplace=True)
        
        # Validate OHLC relationships
        if validate_ohlc:
            df = self._fix_ohlc_relationships(df)
        
        # Remove outliers
        if remove_outliers:
            df = self._remove_price_outliers(df)
        
        # Fill missing values
        if fill_missing:
            df = self._fill_missing_values(df)
        
        # Remove duplicate timestamps
        df = df[~df.index.duplicated(keep='first')]
        
        # Sort by timestamp
        df.sort_index(inplace=True)
        
        logger.info(f"Cleaned price data for {symbol}, final shape: {df.shape}")
        
        return MarketData(df, symbol)
    
    def _fix_ohlc_relationships(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fix invalid OHLC relationships."""
        initial_count = len(df)
        
        # Remove rows where high < low
        invalid_high_low = df['high'] < df['low']
        df = df[~invalid_high_low]
        
        # Remove rows where open/close are outside high/low range
        invalid_open = (df['open'] < df['low']) | (df['open'] > df['high'])
        invalid_close = (df['close'] < df['low']) | (df['close'] > df['high'])
        
        df = df[~(invalid_open | invalid_close)]
        
        removed_count = initial_count - len(df)
        if removed_count > 0:
            logger.warning(f"Removed {removed_count} rows with invalid OHLC relationships")
        
        return df
    
    def _remove_price_outliers(self, df: pd.DataFrame, z_threshold: float = 5.0) -> pd.DataFrame:
        """Remove price outliers using z-score method."""
        initial_count = len(df)
        
        # Calculate returns for outlier detection
        returns = df['close'].pct_change().dropna()
        
        if len(returns) == 0:
            return df
        
        # Calculate z-scores
        z_scores = np.abs((returns - returns.mean()) / returns.std())
        
        # Find outliers
        outlier_indices = returns[z_scores > z_threshold].index
        
        # Remove outliers
        df = df.drop(outlier_indices, errors='ignore')
        
        removed_count = initial_count - len(df)
        if removed_count > 0:
            logger.warning(f"Removed {removed_count} outlier rows")
        
        return df
    
    def _fill_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fill missing values using forward fill method."""
        missing_before = df.isnull().sum().sum()
        
        if missing_before == 0:
            return df
        
        # Forward fill missing values
        df.fillna(method='ffill', inplace=True)
        
        # If there are still missing values at the beginning, use backward fill
        df.fillna(method='bfill', inplace=True)
        
        missing_after = df.isnull().sum().sum()
        filled_count = missing_before - missing_after
        
        if filled_count > 0:
            logger.info(f"Filled {filled_count} missing values")
        
        return df
    
    def remove_low_volume_periods(
        self,
        market_data: MarketData,
        min_volume: float = 1000
    ) -> MarketData:
        """
        Remove periods with very low volume.
        
        Args:
            market_data: MarketData to filter
            min_volume: Minimum volume threshold
            
        Returns:
            Filtered MarketData
        """
        df = market_data.data.copy()
        initial_count = len(df)
        
        # Filter out low volume periods
        df = df[df['volume'] >= min_volume]
        
        removed_count = initial_count - len(df)
        if removed_count > 0:
            logger.info(f"Removed {removed_count} low volume periods for {market_data.symbol}")
        
        return MarketData(df, market_data.symbol)
    
    def adjust_for_splits(
        self,
        market_data: MarketData,
        splits: pd.DataFrame
    ) -> MarketData:
        """
        Adjust price data for stock splits.
        
        Args:
            market_data: MarketData to adjust
            splits: DataFrame with split information
            
        Returns:
            Split-adjusted MarketData
        """
        if splits.empty:
            return market_data
        
        df = market_data.data.copy()
        
        # Apply split adjustments
        for split_date, split_ratio in splits.items():
            # Adjust prices before split date
            mask = df.index < split_date
            
            price_columns = ['open', 'high', 'low', 'close', 'adjusted_close']
            df.loc[mask, price_columns] = df.loc[mask, price_columns] / split_ratio
            
            # Adjust volume after split date
            df.loc[~mask, 'volume'] = df.loc[~mask, 'volume'] * split_ratio
        
        logger.info(f"Applied {len(splits)} split adjustments for {market_data.symbol}")
        
        return MarketData(df, market_data.symbol)
    
    def resample_data(
        self,
        market_data: MarketData,
        frequency: str,
        method: str = 'ohlc'
    ) -> MarketData:
        """
        Resample data to different frequency.
        
        Args:
            market_data: MarketData to resample
            frequency: Target frequency (e.g., '1H', '1D', '1W')
            method: Resampling method ('ohlc' or 'last')
            
        Returns:
            Resampled MarketData
        """
        df = market_data.data.copy()
        
        if method == 'ohlc':
            resampled = df.resample(frequency).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum',
                'adjusted_close': 'last'
            })
        elif method == 'last':
            resampled = df.resample(frequency).last()
        else:
            raise ValueError(f"Unsupported resampling method: {method}")
        
        # Remove periods with no data
        resampled.dropna(inplace=True)
        
        logger.info(f"Resampled {market_data.symbol} from {len(df)} to {len(resampled)} periods")
        
        return MarketData(resampled, market_data.symbol)
    
    def align_data(
        self,
        market_data_dict: Dict[str, MarketData],
        method: str = 'inner'
    ) -> Dict[str, MarketData]:
        """
        Align multiple MarketData objects to have the same timestamps.
        
        Args:
            market_data_dict: Dictionary of symbol -> MarketData
            method: Alignment method ('inner', 'outer', 'left', 'right')
            
        Returns:
            Dictionary of aligned MarketData objects
        """
        if len(market_data_dict) < 2:
            return market_data_dict
        
        # Get all dataframes
        dfs = {symbol: md.data for symbol, md in market_data_dict.items()}
        
        # Find common date range based on method
        if method == 'inner':
            # Use intersection of all date ranges
            start_date = max(df.index.min() for df in dfs.values())
            end_date = min(df.index.max() for df in dfs.values())
        elif method == 'outer':
            # Use union of all date ranges
            start_date = min(df.index.min() for df in dfs.values())
            end_date = max(df.index.max() for df in dfs.values())
        else:
            raise ValueError(f"Unsupported alignment method: {method}")
        
        # Filter all dataframes to common date range
        aligned_data = {}
        for symbol, df in dfs.items():
            filtered_df = df[(df.index >= start_date) & (df.index <= end_date)]
            if not filtered_df.empty:
                aligned_data[symbol] = MarketData(filtered_df, symbol)
        
        logger.info(f"Aligned {len(aligned_data)} symbols to date range {start_date} - {end_date}")
        
        return aligned_data
