"""Base data provider interface for the Mean Reversion Trading System."""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Dict, Any
import pandas as pd

from ..models import PriceData, Symbol, MarketData


class DataProvider(ABC):
    """Abstract base class for data providers."""
    
    def __init__(self, name: str, rate_limit: int = 1000):
        """
        Initialize data provider.
        
        Args:
            name: Provider name
            rate_limit: Maximum requests per hour
        """
        self.name = name
        self.rate_limit = rate_limit
        self._request_count = 0
        self._last_reset = datetime.now()
    
    @abstractmethod
    def get_price_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1d"
    ) -> Optional[MarketData]:
        """
        Get historical price data for a symbol.
        
        Args:
            symbol: Trading symbol
            start_date: Start date
            end_date: End date
            interval: Data interval (1d, 1h, 5m, etc.)
            
        Returns:
            MarketData object or None if not found
        """
        pass
    
    @abstractmethod
    def get_latest_price(self, symbol: str) -> Optional[PriceData]:
        """
        Get latest price for a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Latest PriceData or None if not found
        """
        pass
    
    @abstractmethod
    def search_symbols(self, query: str) -> List[Symbol]:
        """
        Search for symbols matching query.
        
        Args:
            query: Search query
            
        Returns:
            List of matching symbols
        """
        pass
    
    @abstractmethod
    def get_symbol_info(self, symbol: str) -> Optional[Symbol]:
        """
        Get detailed information about a symbol.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Symbol object with metadata or None if not found
        """
        pass
    
    @abstractmethod
    def is_market_open(self) -> bool:
        """
        Check if market is currently open.
        
        Returns:
            True if market is open
        """
        pass
    
    @abstractmethod
    def get_supported_intervals(self) -> List[str]:
        """
        Get list of supported data intervals.
        
        Returns:
            List of interval strings
        """
        pass
    
    def get_multiple_price_data(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        interval: str = "1d"
    ) -> Dict[str, MarketData]:
        """
        Get price data for multiple symbols.
        
        Args:
            symbols: List of trading symbols
            start_date: Start date
            end_date: End date
            interval: Data interval
            
        Returns:
            Dictionary mapping symbols to MarketData
        """
        result = {}
        
        for symbol in symbols:
            try:
                market_data = self.get_price_data(symbol, start_date, end_date, interval)
                if market_data:
                    result[symbol] = market_data
            except Exception as e:
                print(f"Error fetching data for {symbol}: {e}")
                continue
        
        return result
    
    def _check_rate_limit(self) -> None:
        """Check and enforce rate limits."""
        now = datetime.now()
        
        # Reset counter every hour
        if (now - self._last_reset).total_seconds() > 3600:
            self._request_count = 0
            self._last_reset = now
        
        if self._request_count >= self.rate_limit:
            raise RuntimeError(f"Rate limit exceeded for {self.name}")
        
        self._request_count += 1
    
    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol format for the provider."""
        return symbol.upper().strip()
    
    def _validate_date_range(self, start_date: datetime, end_date: datetime) -> None:
        """Validate date range."""
        if start_date >= end_date:
            raise ValueError("Start date must be before end date")
        
        if end_date > datetime.now():
            raise ValueError("End date cannot be in the future")
    
    def _convert_to_price_data(self, df: pd.DataFrame, symbol: str) -> List[PriceData]:
        """Convert DataFrame to list of PriceData objects."""
        price_data_list = []
        
        for timestamp, row in df.iterrows():
            price_data = PriceData(
                symbol=symbol,
                timestamp=timestamp,
                open=row['open'],
                high=row['high'],
                low=row['low'],
                close=row['close'],
                volume=row['volume'],
                adjusted_close=row.get('adjusted_close', row['close'])
            )
            price_data_list.append(price_data)
        
        return price_data_list
    
    def __repr__(self) -> str:
        """String representation."""
        return f"{self.__class__.__name__}(name='{self.name}', rate_limit={self.rate_limit})"
