"""Yahoo Finance data provider for the Mean Reversion Trading System."""

import yfinance as yf
from datetime import datetime, time
from typing import List, Optional, Dict, Any
import pandas as pd
import pytz

from .base import DataProvider
from ..models import PriceData, Symbol, MarketData
from ...utils.logger import get_logger
from ...utils.decorators import retry

logger = get_logger(__name__)


class YahooFinanceProvider(DataProvider):
    """Yahoo Finance data provider implementation."""
    
    def __init__(self, rate_limit: int = 2000):
        """
        Initialize Yahoo Finance provider.
        
        Args:
            rate_limit: Maximum requests per hour
        """
        super().__init__("Yahoo Finance", rate_limit)
        
        # Yahoo Finance supported intervals
        self._supported_intervals = [
            "1m", "2m", "5m", "15m", "30m", "60m", "90m",
            "1h", "1d", "5d", "1wk", "1mo", "3mo"
        ]
    
    @retry(max_attempts=3, delay=1.0)
    def get_price_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = "1d"
    ) -> Optional[MarketData]:
        """Get historical price data from Yahoo Finance."""
        self._check_rate_limit()
        self._validate_date_range(start_date, end_date)
        
        symbol = self._normalize_symbol(symbol)
        
        if interval not in self._supported_intervals:
            raise ValueError(f"Unsupported interval: {interval}")
        
        try:
            ticker = yf.Ticker(symbol)
            
            # Download data
            df = ticker.history(
                start=start_date.date(),
                end=end_date.date(),
                interval=interval,
                auto_adjust=False,
                prepost=True,
                threads=True
            )
            
            if df.empty:
                logger.warning(f"No data found for symbol: {symbol}")
                return None
            
            # Normalize column names
            df.columns = df.columns.str.lower()
            
            # Rename columns to match our schema
            column_mapping = {
                'adj close': 'adjusted_close'
            }
            df.rename(columns=column_mapping, inplace=True)
            
            # Ensure we have adjusted_close column
            if 'adjusted_close' not in df.columns:
                df['adjusted_close'] = df['close']
            
            # Remove any rows with NaN values
            df.dropna(inplace=True)
            
            if df.empty:
                logger.warning(f"No valid data after cleaning for symbol: {symbol}")
                return None
            
            logger.debug(f"Retrieved {len(df)} records for {symbol} from Yahoo Finance")
            return MarketData(df, symbol)
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol} from Yahoo Finance: {e}")
            return None
    
    @retry(max_attempts=3, delay=0.5)
    def get_latest_price(self, symbol: str) -> Optional[PriceData]:
        """Get latest price from Yahoo Finance."""
        self._check_rate_limit()
        
        symbol = self._normalize_symbol(symbol)
        
        try:
            ticker = yf.Ticker(symbol)
            
            # Get latest data (last 2 days to ensure we get the most recent)
            df = ticker.history(period="2d", interval="1d")
            
            if df.empty:
                logger.warning(f"No latest price data found for symbol: {symbol}")
                return None
            
            # Get the most recent row
            latest_row = df.iloc[-1]
            latest_timestamp = df.index[-1]
            
            return PriceData(
                symbol=symbol,
                timestamp=latest_timestamp.to_pydatetime(),
                open=latest_row['Open'],
                high=latest_row['High'],
                low=latest_row['Low'],
                close=latest_row['Close'],
                volume=latest_row['Volume'],
                adjusted_close=latest_row.get('Adj Close', latest_row['Close'])
            )
            
        except Exception as e:
            logger.error(f"Error fetching latest price for {symbol}: {e}")
            return None
    
    def search_symbols(self, query: str) -> List[Symbol]:
        """Search for symbols (limited functionality with yfinance)."""
        # Note: yfinance doesn't have a built-in search function
        # This is a basic implementation that could be enhanced
        logger.warning("Symbol search functionality is limited with Yahoo Finance")
        return []
    
    @retry(max_attempts=3, delay=0.5)
    def get_symbol_info(self, symbol: str) -> Optional[Symbol]:
        """Get symbol information from Yahoo Finance."""
        self._check_rate_limit()
        
        symbol = self._normalize_symbol(symbol)
        
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if not info or 'symbol' not in info:
                logger.warning(f"No info found for symbol: {symbol}")
                return None
            
            # Determine asset type
            asset_type = "stock"  # default
            if symbol.endswith("-USD") or symbol.endswith("USD"):
                asset_type = "crypto"
            elif "=" in symbol:
                asset_type = "forex"
            
            return Symbol(
                symbol=symbol,
                name=info.get('longName', info.get('shortName')),
                exchange=info.get('exchange'),
                asset_type=asset_type,
                currency=info.get('currency', 'USD'),
                sector=info.get('sector'),
                industry=info.get('industry'),
                market_cap=info.get('marketCap'),
                is_active=True,
                metadata={
                    'yahoo_info': info,
                    'provider': self.name
                }
            )
            
        except Exception as e:
            logger.error(f"Error fetching symbol info for {symbol}: {e}")
            return None
    
    def is_market_open(self) -> bool:
        """Check if US market is open (simplified)."""
        # This is a simplified implementation
        # In practice, you'd want to check specific market hours and holidays
        
        now = datetime.now(pytz.timezone('US/Eastern'))
        current_time = now.time()
        
        # Check if it's a weekday
        if now.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False
        
        # Check if it's during market hours (9:30 AM - 4:00 PM ET)
        market_open = time(9, 30)
        market_close = time(16, 0)
        
        return market_open <= current_time <= market_close
    
    def get_supported_intervals(self) -> List[str]:
        """Get supported intervals."""
        return self._supported_intervals.copy()
    
    def get_dividends(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Get dividend data for a symbol."""
        self._check_rate_limit()
        
        symbol = self._normalize_symbol(symbol)
        
        try:
            ticker = yf.Ticker(symbol)
            dividends = ticker.dividends
            
            # Filter by date range
            if not dividends.empty:
                dividends = dividends[(dividends.index >= start_date) & (dividends.index <= end_date)]
            
            return dividends
            
        except Exception as e:
            logger.error(f"Error fetching dividends for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_splits(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Get stock split data for a symbol."""
        self._check_rate_limit()
        
        symbol = self._normalize_symbol(symbol)
        
        try:
            ticker = yf.Ticker(symbol)
            splits = ticker.splits
            
            # Filter by date range
            if not splits.empty:
                splits = splits[(splits.index >= start_date) & (splits.index <= end_date)]
            
            return splits
            
        except Exception as e:
            logger.error(f"Error fetching splits for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_financials(self, symbol: str) -> Dict[str, pd.DataFrame]:
        """Get financial statements for a symbol."""
        self._check_rate_limit()
        
        symbol = self._normalize_symbol(symbol)
        
        try:
            ticker = yf.Ticker(symbol)
            
            return {
                'income_statement': ticker.financials,
                'balance_sheet': ticker.balance_sheet,
                'cash_flow': ticker.cashflow
            }
            
        except Exception as e:
            logger.error(f"Error fetching financials for {symbol}: {e}")
            return {}
    
    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol for Yahoo Finance format."""
        symbol = super()._normalize_symbol(symbol)
        
        # Handle common symbol format conversions
        # This could be expanded based on specific needs
        
        return symbol
