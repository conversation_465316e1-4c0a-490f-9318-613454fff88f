# Environment variables for Mean Reversion Trading System
# Copy this file to .env and fill in your actual values

# Data Provider API Keys
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

# Database Configuration
DATABASE_URL=sqlite:///data/market_data.db

# Email Notifications (optional)
SMTP_SERVER=smtp.gmail.com
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password_here

# Slack Notifications (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Development Settings
DEBUG=false
LOG_LEVEL=INFO
