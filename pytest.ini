[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --cov=src
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
    --strict-markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    data: Tests requiring external data
    api: Tests requiring API access
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
